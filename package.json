{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "seed": "prisma db seed"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seeder/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.8.0", "@emotion/is-prop-valid": "latest", "@faker-js/faker": "latest", "@hookform/resolvers": "latest", "@prisma/client": "^6.5.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@types/bcrypt": "^6.0.0", "autoprefixer": "^10.4.20", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "critters": "^0.0.25", "date-fns": "latest", "embla-carousel-react": "8.5.1", "framer-motion": "latest", "fs": "latest", "glob": "latest", "immer": "latest", "input-otp": "1.4.1", "js-yaml": "latest", "lodash": "latest", "lucide-react": "^0.454.0", "next": "15.1.0", "next-auth": "^4.24.11", "next-themes": "latest", "path": "latest", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "latest", "react-resizable-panels": "^2.1.7", "recharts": "latest", "resend": "^4.2.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "url": "latest", "use-sync-external-store": "latest", "vaul": "^0.9.6", "zod": "latest", "zustand": "latest"}, "devDependencies": {"@types/js-yaml": "^4.0.9", "@types/lodash": "^4.17.16", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "prisma": "^6.5.0", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5"}}