// app/api/contact/route.ts

import { Resend } from "resend"
import { type NextRequest, NextResponse } from "next/server"

const resend = new Resend(process.env.RESEND_API_KEY)

export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const { firstName, lastName, email, message } = body

    const data = await resend.emails.send({
      from: "Acme <<EMAIL>>",
      to: ["<EMAIL>"],
      subject: "Message from Contact Form",
      html: `
        <p>First Name: ${firstName}</p>
        <p>Last Name: ${lastName}</p>
        <p>Email: ${email}</p>
        <p>Message: ${message}</p>
      `,
    })

    return NextResponse.json({ data })
  } catch (error) {
    return NextResponse.json({ error })
  }
}
