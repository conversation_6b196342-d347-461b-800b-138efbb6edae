model Account { … }              -- OAuth provider accounts
model Session { … }              -- User sessions
model User { … }                 -- Application users
model VerificationToken { … }    -- NextAuth verification tokens
model Comment { … }              -- User comments
model Subscription { … }         -- Stripe subscription records

-- SQL schema for Anatomy Explorer database (PostgreSQL)

-- Users table
CREATE TABLE IF NOT EXISTS "User" (
    id              TEXT PRIMARY KEY,
    name            TEXT,
    email           TEXT UNIQUE,
    "emailVerified" TIMESTAMPTZ,
    password        TEXT,
    image           TEXT,
    role            TEXT NOT NULL DEFAULT 'user',
    status          TEXT NOT NULL DEFAULT 'active',
    "createdAt"     TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "updatedAt"     TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Accounts table for authentication providers
CREATE TABLE IF NOT EXISTS "Account" (
    id                TEXT PRIMARY KEY,
    "userId"          TEXT NOT NULL,
    type              TEXT NOT NULL,
    provider          TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    refresh_token     TEXT,
    access_token      TEXT,
    expires_at        INTEGER,
    token_type        TEXT,
    scope             TEXT,
    id_token          TEXT,
    session_state     TEXT,
    CONSTRAINT "Account_userId_fkey"
        FOREIGN KEY ("userId") REFERENCES "User"(id) ON DELETE CASCADE
);
CREATE UNIQUE INDEX IF NOT EXISTS "Account_provider_providerAccountId_key"
    ON "Account" (provider, "providerAccountId");
CREATE INDEX IF NOT EXISTS "Account_userId_idx" ON "Account" ("userId");

-- Sessions table
CREATE TABLE IF NOT EXISTS "Session" (
    id           TEXT PRIMARY KEY,
    "sessionToken" TEXT NOT NULL UNIQUE,
    "userId"     TEXT NOT NULL,
    expires      TIMESTAMPTZ NOT NULL,
    CONSTRAINT "Session_userId_fkey"
        FOREIGN KEY ("userId") REFERENCES "User"(id) ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "Session_userId_idx" ON "Session" ("userId");

-- Verification tokens
CREATE TABLE IF NOT EXISTS "VerificationToken" (
    identifier TEXT NOT NULL,
    token      TEXT NOT NULL,
    expires    TIMESTAMPTZ NOT NULL,
    CONSTRAINT "VerificationToken_token_key" UNIQUE (token),
    CONSTRAINT "VerificationToken_identifier_token_key" UNIQUE (identifier, token)
);

-- Comments table
CREATE TABLE IF NOT EXISTS "Comment" (
    id         SERIAL PRIMARY KEY,
    "createdAt" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    content    TEXT NOT NULL,
    "authorId" TEXT NOT NULL,
    "postId"   INTEGER,
    approved   BOOLEAN NOT NULL DEFAULT FALSE,
    CONSTRAINT "Comment_authorId_fkey"
        FOREIGN KEY ("authorId") REFERENCES "User"(id) ON DELETE CASCADE
);
CREATE INDEX IF NOT EXISTS "Comment_authorId_idx" ON "Comment" ("authorId");

-- Subscription table
CREATE TABLE IF NOT EXISTS "Subscription" (
    id                   TEXT PRIMARY KEY,
    "userId"             TEXT NOT NULL UNIQUE,
    "stripeCustomerId"     TEXT UNIQUE,
    "stripeSubscriptionId" TEXT UNIQUE,
    "planId"             TEXT,
    status               TEXT,
    "trialEndsAt"        TIMESTAMPTZ,
    "currentPeriodEnd"   TIMESTAMPTZ,
    "createdAt"          TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "updatedAt"          TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT "Subscription_userId_fkey"
        FOREIGN KEY ("userId") REFERENCES "User"(id) ON DELETE CASCADE
);
