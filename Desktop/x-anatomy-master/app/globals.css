@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Fallback Model SVG Colors */
    --fallback-body-fill: theme(colors.slate.300); /* Lighter for light theme */
    --fallback-body-stroke: theme(colors.slate.500);
    --fallback-muscle-fill: theme(colors.primary.DEFAULT / 0.2);
    --fallback-muscle-stroke: theme(colors.primary.DEFAULT / 0.4);
    --fallback-muscle-fill-hover: theme(colors.primary.DEFAULT / 0.5);
    --fallback-muscle-stroke-hover: theme(colors.primary.DEFAULT / 0.8);
    --fallback-label-circle-fill: theme(colors.background / 0.8);
    --fallback-label-circle-stroke: theme(colors.primary.DEFAULT / 0.8);
    --fallback-label-circle-fill-hover: theme(colors.primary.DEFAULT / 0.9);
    --fallback-label-circle-stroke-hover: theme(colors.primary.foreground);
    --fallback-label-text: theme(colors.foreground);
    --fallback-label-text-hover: theme(colors.primary.foreground);

    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    /* Fallback Model SVG Colors (Dark Theme) */
    --fallback-body-fill: theme(colors.slate.700); /* Darker for dark theme */
    --fallback-body-stroke: theme(colors.slate.500);
    /* Muscle colors can often remain similar, relying on primary */
    --fallback-muscle-fill: theme(colors.primary.DEFAULT / 0.2);
    --fallback-muscle-stroke: theme(colors.primary.DEFAULT / 0.4);
    --fallback-muscle-fill-hover: theme(colors.primary.DEFAULT / 0.5);
    --fallback-muscle-stroke-hover: theme(colors.primary.DEFAULT / 0.8);
    --fallback-label-circle-fill: theme(colors.background / 0.8);
    --fallback-label-circle-stroke: theme(colors.primary.DEFAULT / 0.8);
    --fallback-label-circle-fill-hover: theme(colors.primary.DEFAULT / 0.9);
    --fallback-label-circle-stroke-hover: theme(colors.primary.foreground);
    --fallback-label-text: theme(colors.foreground);
    --fallback-label-text-hover: theme(colors.primary.foreground);

    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}
